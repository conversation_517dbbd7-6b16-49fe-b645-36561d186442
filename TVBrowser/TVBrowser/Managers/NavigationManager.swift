//
//  NavigationManager.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import Combine
import Observation

// MARK: - Navigation Manager

class NavigationManager: ObservableObject, Observable {
    static let shared = NavigationManager()
    
    @Published var currentScreen: AppScreen = .home
    @Published var navigationStack: [AppScreen] = []
    @Published var isTransitioning = false
    @Published var selectedTab: Int = 0
    
    // Modal presentations
    @Published var presentedMovie: Movie?
    @Published var presentedCategory: Category?
    @Published var isShowingMovieDetail = false
    @Published var isShowingVideoPlayer = false
    @Published var isShowingCategoryView = false
    @Published var isShowingCategoryBrowser = false
    @Published var currentEpisode: Episode?
    
    // Navigation history
    @Published var canGoBack = false
    @Published var canGoForward = false
    private var backStack: [AppScreen] = []
    private var forwardStack: [AppScreen] = []
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupNavigationObservation()
    }
    
    // MARK: - Navigation Methods
    
    func navigate(to screen: AppScreen, animated: Bool = true) {
        guard screen != currentScreen else { return }
        
        if animated {
            withAnimation(.easeInOut(duration: 0.3)) {
                performNavigation(to: screen)
            }
        } else {
            performNavigation(to: screen)
        }
    }
    
    private func performNavigation(to screen: AppScreen) {
        // Add current screen to back stack
        if currentScreen != .home {
            backStack.append(currentScreen)
        }
        
        // Clear forward stack when navigating to new screen
        forwardStack.removeAll()
        
        // Update current screen
        currentScreen = screen
        navigationStack.append(screen)
        
        // Update navigation state
        updateNavigationState()
        
        // Update focus section based on screen
        updateFocusSection(for: screen)
    }
    
    func goBack() {
        guard canGoBack, let previousScreen = backStack.popLast() else { return }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            // Add current screen to forward stack
            forwardStack.append(currentScreen)
            
            // Navigate to previous screen
            currentScreen = previousScreen
            if let lastIndex = navigationStack.lastIndex(of: currentScreen) {
                navigationStack.removeLast(navigationStack.count - lastIndex - 1)
            }
            
            updateNavigationState()
            updateFocusSection(for: previousScreen)
        }
    }
    
    func goForward() {
        guard canGoForward, let nextScreen = forwardStack.popLast() else { return }
        
        withAnimation(.easeInOut(duration: 0.3)) {
            // Add current screen to back stack
            backStack.append(currentScreen)
            
            // Navigate to next screen
            currentScreen = nextScreen
            navigationStack.append(nextScreen)
            
            updateNavigationState()
            updateFocusSection(for: nextScreen)
        }
    }
    
    func goHome() {
        withAnimation(.easeInOut(duration: 0.3)) {
            // Clear stacks
            backStack.removeAll()
            forwardStack.removeAll()
            navigationStack = [.home]
            
            // Navigate to home
            currentScreen = .home
            selectedTab = 0
            
            updateNavigationState()
            updateFocusSection(for: .home)
        }
    }
    
    // MARK: - Modal Presentations
    
    func presentMovieDetail(for movie: Movie) {
        presentedMovie = movie
        isShowingMovieDetail = true
        
        // Update focus
        FocusManager.shared.focusedSection = .movieDetail
    }
    
    func dismissMovieDetail() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowingMovieDetail = false
            presentedMovie = nil
        }

        // Restore previous focus
        updateFocusSection(for: currentScreen)
    }

    func presentCategoryView(for category: Category) {
        presentedCategory = category
        isShowingCategoryView = true

        // Update focus
        FocusManager.shared.focusedSection = .category
    }

    func dismissCategoryView() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowingCategoryView = false
            presentedCategory = nil
        }

        // Restore previous focus
        updateFocusSection(for: currentScreen)
    }

    func presentCategoryBrowser() {
        isShowingCategoryBrowser = true

        // Update focus
        FocusManager.shared.focusedSection = .category
    }

    func dismissCategoryBrowser() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowingCategoryBrowser = false
        }

        // Restore previous focus
        updateFocusSection(for: currentScreen)
    }
    
    func presentVideoPlayer(for episode: Episode, movie: Movie) {
        currentEpisode = episode
        presentedMovie = movie
        isShowingVideoPlayer = true
        
        // Update focus
        FocusManager.shared.focusedSection = .videoPlayer
    }
    
    func dismissVideoPlayer() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isShowingVideoPlayer = false
            currentEpisode = nil
        }
        
        // Restore previous focus
        FocusManager.shared.focusedSection = .movieDetail
    }
    
    // MARK: - Tab Management
    
    func selectTab(_ index: Int) {
        guard index != selectedTab else { return }
        
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedTab = index
            
            switch index {
            case 0:
                navigate(to: .home)
            case 1:
                navigate(to: .search)
            default:
                break
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func updateNavigationState() {
        canGoBack = !backStack.isEmpty
        canGoForward = !forwardStack.isEmpty
    }
    
    private func updateFocusSection(for screen: AppScreen) {
        let focusSection: FocusSection
        
        switch screen {
        case .home:
            focusSection = .home
        case .search:
            focusSection = .search
        case .movieDetail:
            focusSection = .movieDetail
        case .videoPlayer:
            focusSection = .videoPlayer
        case .settings:
            focusSection = .home // Default to home for settings
        }
        
        FocusManager.shared.focusedSection = focusSection
    }
    
    private func setupNavigationObservation() {
        // Observe screen changes
        $currentScreen
            .sink { screen in
                print("Navigated to: \(screen)")
            }
            .store(in: &cancellables)
        
        // Observe modal presentations
        $isShowingMovieDetail
            .sink { isShowing in
                if isShowing {
                    print("Presenting movie detail")
                } else {
                    print("Dismissing movie detail")
                }
            }
            .store(in: &cancellables)
        
        $isShowingVideoPlayer
            .sink { isShowing in
                if isShowing {
                    print("Presenting video player")
                } else {
                    print("Dismissing video player")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Remote Control Handling
    
    func handleRemoteControl(_ action: RemoteControlAction) {
        switch action {
        case .menu:
            handleMenuButton()
        case .playPause:
            handlePlayPauseButton()
        case .select:
            handleSelectButton()
        case .swipeUp:
            FocusManager.shared.moveFocus(direction: .up)
        case .swipeDown:
            FocusManager.shared.moveFocus(direction: .down)
        case .swipeLeft:
            FocusManager.shared.moveFocus(direction: .left)
        case .swipeRight:
            FocusManager.shared.moveFocus(direction: .right)
        }
    }
    
    private func handleMenuButton() {
        if isShowingVideoPlayer {
            dismissVideoPlayer()
        } else if isShowingMovieDetail {
            dismissMovieDetail()
        } else if canGoBack {
            goBack()
        }
    }
    
    private func handlePlayPauseButton() {
        if isShowingVideoPlayer {
            // Handle play/pause in video player
            NotificationCenter.default.post(name: .videoPlayerTogglePlayPause, object: nil)
        }
    }
    
    private func handleSelectButton() {
        // Handle selection based on current focus
        if let focusedItem = FocusManager.shared.currentFocusedItem {
            handleFocusedItemSelection(focusedItem)
        }
    }
    
    private func handleFocusedItemSelection(_ item: FocusableItem) {
        switch item.type {
        case .movieCard:
            // Present movie detail
            NotificationCenter.default.post(
                name: .presentMovieDetail,
                object: nil,
                userInfo: ["itemIndex": item.index]
            )
        case .playButton:
            // Start video playback
            NotificationCenter.default.post(name: .startVideoPlayback, object: nil)
        case .episodeButton:
            // Play specific episode
            NotificationCenter.default.post(
                name: .playEpisode,
                object: nil,
                userInfo: ["episodeIndex": item.index]
            )
        case .tabButton:
            selectTab(item.index)
        default:
            break
        }
    }
}

// MARK: - App Screen Enum

enum AppScreen: String, CaseIterable {
    case home = "Home"
    case search = "Search"
    case movieDetail = "Movie Detail"
    case videoPlayer = "Video Player"
    case settings = "Settings"
}

// MARK: - Remote Control Actions

enum RemoteControlAction {
    case menu
    case playPause
    case select
    case swipeUp
    case swipeDown
    case swipeLeft
    case swipeRight
}

// MARK: - Navigation Environment

struct NavigationEnvironmentKey: EnvironmentKey {
    static let defaultValue = NavigationManager.shared
}

extension EnvironmentValues {
    var navigationManager: NavigationManager {
        get { self[NavigationEnvironmentKey.self] }
        set { self[NavigationEnvironmentKey.self] = newValue }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let presentMovieDetail = Notification.Name("presentMovieDetail")
    static let startVideoPlayback = Notification.Name("startVideoPlayback")
    static let playEpisode = Notification.Name("playEpisode")
//    static let videoPlayerTogglePlayPause = Notification.Name("videoPlayerTogglePlayPause")
}
