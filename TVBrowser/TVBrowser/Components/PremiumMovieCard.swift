//
//  PremiumMovieCard.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

// MARK: - Premium Movie Card

struct PremiumMovieCard: View {
    let movie: Movie
    let index: Int
    let section: FocusSection
    let onTap: () -> Void
    
    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false
    @State private var isLoading = true
    @State private var imageLoadFailed = false
    @State private var hoverOffset: CGFloat = 0
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Background glow effect
                if isFocused {
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.tvCard + 4)
                        .fill(DesignSystem.Colors.gradientFocus)
                        .frame(
                            width: DesignSystem.Sizes.movieCardWidth + 8,
                            height: DesignSystem.Sizes.movieCardHeight + 8
                        )
                        .blur(radius: 10)
                        .opacity(0.6)
                }
                
                // Main card content
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                    // Poster with loading state
                    posterView
                    
                    // Movie information
                    movieInfoView
                }
                .frame(width: DesignSystem.Sizes.movieCardWidth)
            }
        }
        .buttonStyle(TVButtonStyle())
        .focusAnimation(
            isFocused: isFocused,
            scale: DesignSystem.Sizes.focusScale,
            shadowRadius: 25
        )
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            withAnimation(DesignSystem.Animations.focus) {
                isFocused = focusedItem?.type == .movieCard && 
                           focusedItem?.index == index &&
                           focusedItem?.section == section
            }
        }
        .onAppear {
            // Staggered animation for card appearance
            withAnimation(
                DesignSystem.Animations.slideIn.delay(Double(index) * 0.1)
            ) {
                hoverOffset = 0
            }
        }
    }
    
    // MARK: - Poster View
    
    private var posterView: some View {
        ZStack {
            // Poster image
            AsyncImage(url: URL(string: movie.poster ?? "")) { phase in
                switch phase {
                case .empty:
                    posterPlaceholder
                        .shimmer()
                case .success(let image):
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                        .onAppear {
                            withAnimation(DesignSystem.Animations.fadeIn) {
                                isLoading = false
                            }
                        }
                case .failure(_):
                    posterError
                        .onAppear {
                            imageLoadFailed = true
                            isLoading = false
                        }
                @unknown default:
                    posterPlaceholder
                }
            }
            .frame(
                width: DesignSystem.Sizes.movieCardWidth,
                height: DesignSystem.Sizes.movieCardHeight
            )
            .clipShape(RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.tvCard))
            
            // Overlay effects
            if isFocused {
                // Gradient overlay for better text readability
                LinearGradient(
                    colors: [Color.clear, Color.black.opacity(0.7)],
                    startPoint: .center,
                    endPoint: .bottom
                )
                .clipShape(RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.tvCard))
                
                // Quality badge
                if let remarks = movie.remarks, !remarks.isEmpty {
                    VStack {
                        HStack {
                            Spacer()
                            qualityBadge(remarks)
                        }
                        Spacer()
                    }
                    .padding(DesignSystem.Spacing.sm)
                }
                
                // Play icon overlay
                playIconOverlay
            }
        }
        .designSystemShadow(
            isFocused ? DesignSystem.Shadows.focusStrong : DesignSystem.Shadows.medium
        )
    }
    
    // MARK: - Poster States
    
    private var posterPlaceholder: some View {
        Rectangle()
            .fill(DesignSystem.Colors.surface)
            .overlay(
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "photo")
                        .font(.system(size: 40, weight: .light))
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                    
                    if isLoading {
                        PulsingDots()
                    }
                }
            )
    }
    
    private var posterError: some View {
        Rectangle()
            .fill(DesignSystem.Colors.surface)
            .overlay(
                VStack(spacing: DesignSystem.Spacing.sm) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 32, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.error)
                    
                    Text("加载失败")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
            )
    }
    
    // MARK: - Overlay Components
    
    private var playIconOverlay: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                
                ZStack {
                    Circle()
                        .fill(Color.black.opacity(0.7))
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: "play.fill")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.white)
                        .offset(x: 2) // Optical alignment
                }
                .scaleEffect(isFocused ? 1.0 : 0.8)
                .opacity(isFocused ? 1.0 : 0.0)
                .animation(
                    DesignSystem.Animations.scaleUp.delay(0.1),
                    value: isFocused
                )
                
                Spacer()
            }
            Spacer()
        }
    }
    
    private func qualityBadge(_ quality: String) -> some View {
        Text(quality)
            .font(DesignSystem.Typography.labelSmall)
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.sm)
                    .fill(DesignSystem.Colors.accent.opacity(0.8))
            )
            .scaleEffect(isFocused ? 1.0 : 0.8)
            .opacity(isFocused ? 1.0 : 0.0)
            .animation(
                DesignSystem.Animations.scaleUp.delay(0.2),
                value: isFocused
            )
    }
    
    // MARK: - Movie Info View
    
    private var movieInfoView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
            // Title
            Text(movie.title)
                .font(DesignSystem.Typography.titleMedium)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Metadata
            HStack(spacing: DesignSystem.Spacing.xs) {
                // Year
                Text(movie.displayYear)
                    .font(DesignSystem.Typography.labelMedium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                // Separator
                Circle()
                    .fill(DesignSystem.Colors.textSecondary)
                    .frame(width: 3, height: 3)
                
                // Type
                Text(movie.displayType)
                    .font(DesignSystem.Typography.labelMedium)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(1)
                
                Spacer()
            }
            
            // Source info (only when focused)
            if isFocused {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "server.rack")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.accent)
                    
                    Text(movie.sourceName)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.accent)
                }
                .transition(.opacity.combined(with: .move(edge: .bottom)))
                .animation(
                    DesignSystem.Animations.fadeIn.delay(0.3),
                    value: isFocused
                )
            }
        }
        .frame(width: DesignSystem.Sizes.movieCardWidth, alignment: .leading)
    }
}

// MARK: - Premium Category Card

struct PremiumCategoryCard: View {
    let category: Category
    let index: Int
    let onTap: () -> Void

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false
    @State private var animationOffset: CGFloat = 50

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // Background with gradient
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.tvButton)
                    .fill(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.accent.opacity(isFocused ? 0.8 : 0.6),
                                DesignSystem.Colors.accent.opacity(isFocused ? 0.6 : 0.4)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(
                        width: DesignSystem.Sizes.categoryCardWidth,
                        height: DesignSystem.Sizes.categoryCardHeight
                    )
                
                // Category name
                Text(category.name)
                    .font(DesignSystem.Typography.headlineSmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .fontWeight(isFocused ? .bold : .semibold)
                
                // Focus ring
                if isFocused {
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.tvButton)
                        .stroke(DesignSystem.Colors.focusRing, lineWidth: 2)
                        .frame(
                            width: DesignSystem.Sizes.categoryCardWidth,
                            height: DesignSystem.Sizes.categoryCardHeight
                        )
                }
            }
        }
        .buttonStyle(TVButtonStyle())
        .focusAnimation(
            isFocused: isFocused,
            scale: DesignSystem.Sizes.focusScaleSmall
        )
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            withAnimation(DesignSystem.Animations.focus) {
                isFocused = focusedItem?.type == .categoryCard && 
                           focusedItem?.index == index &&
                           focusedItem?.section == .home
            }
        }
        .offset(y: animationOffset)
        .onAppear {
            withAnimation(
                DesignSystem.Animations.slideIn.delay(Double(index) * 0.1)
            ) {
                animationOffset = 0
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: 30) {
        PremiumMovieCard(
            movie: Movie.sample,
            index: 0,
            section: .home,
            onTap: {}
        )
        
        PremiumCategoryCard(
            category: Category(id: 1, name: "电影", type: "1", categoryType: .content),
            index: 0,
            onTap: {}
        )
    }
    .padding()
    .designSystemBackground()
}
