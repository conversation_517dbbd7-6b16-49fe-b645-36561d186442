//
//  ContentView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/23.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var navigationManager = NavigationManager.shared
    @StateObject private var focusManager = FocusManager.shared
    @StateObject private var videoPlayerService = VideoPlayerService.shared

    var body: some View {
        ZStack {
            // Main content
            TabView(selection: $navigationManager.selectedTab) {
                EnhancedHomeView()
                    .tabItem {
                        Image(systemName: "house.fill")
                        Text("首页")
                    }
                    .tag(0)

                EnhancedSearchView()
                    .tabItem {
                        Image(systemName: "magnifyingglass")
                        Text("搜索")
                    }
                    .tag(1)

                EnhancedSettingsView()
                    .tabItem {
                        Image(systemName: "gearshape.fill")
                        Text("设置")
                    }
                    .tag(2)
            }
            .preferredColorScheme(.dark)
            .accentColor(.white)
            .environment(\.navigationManager, navigationManager)
            .environment(\.focusManager, focusManager)

            // Global overlays
            if navigationManager.isTransitioning {
                TransitionOverlay()
            }
        }
        .sheet(isPresented: $navigationManager.isShowingMovieDetail) {
            if let movie = navigationManager.presentedMovie {
                EnhancedMovieDetailView(movie: movie)
                    .environment(\.navigationManager, navigationManager)
                    .environment(\.focusManager, focusManager)
            }
        }
        .sheet(isPresented: $navigationManager.isShowingCategoryView) {
            if let category = navigationManager.presentedCategory {
                CategoryView(category: category)
                    .environment(\.navigationManager, navigationManager)
                    .environment(\.focusManager, focusManager)
            }
        }
        .sheet(isPresented: $navigationManager.isShowingCategoryBrowser) {
            CategoryBrowserView()
                .environment(\.navigationManager, navigationManager)
                .environment(\.focusManager, focusManager)
        }
        .fullScreenCover(isPresented: $navigationManager.isShowingVideoPlayer) {
            if let episode = navigationManager.currentEpisode,
               let movie = navigationManager.presentedMovie {
                EnhancedVideoPlayerView(episode: episode, movie: movie)
                    .environment(\.navigationManager, navigationManager)
                    .environment(\.focusManager, focusManager)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .presentMovieDetail)) { notification in
            if let userInfo = notification.userInfo,
               let itemIndex = userInfo["itemIndex"] as? Int {
                handlePresentMovieDetail(itemIndex: itemIndex)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .startVideoPlayback)) { _ in
            handleStartVideoPlayback()
        }
        .onReceive(NotificationCenter.default.publisher(for: .playEpisode)) { notification in
            if let userInfo = notification.userInfo,
               let episodeIndex = userInfo["episodeIndex"] as? Int {
                handlePlayEpisode(episodeIndex: episodeIndex)
            }
        }
        .onAppear {
            setupInitialFocus()
        }
    }

    // MARK: - Helper Methods

    private func setupInitialFocus() {
        // Set initial focus to first movie card on home screen
        let initialFocus = FocusableItem(
            id: "movie_0",
            type: .movieCard,
            index: 0,
            section: .home
        )
        focusManager.setFocus(to: initialFocus, in: .home)
    }

    private func handlePresentMovieDetail(itemIndex: Int) {
        // This would need to be connected to the actual data source
        // For now, we'll use a placeholder
        if let movie = getMovieForIndex(itemIndex) {
            navigationManager.presentMovieDetail(for: movie)
        }
    }

    private func handleStartVideoPlayback() {
        if let movie = navigationManager.presentedMovie,
           let firstEpisode = movie.episodes?.first {
            navigationManager.presentVideoPlayer(for: firstEpisode, movie: movie)
        }
    }

    private func handlePlayEpisode(episodeIndex: Int) {
        if let movie = navigationManager.presentedMovie,
           let episodes = movie.episodes,
           episodeIndex < episodes.count {
            let episode = episodes[episodeIndex]
            navigationManager.presentVideoPlayer(for: episode, movie: movie)
        }
    }

    private func getMovieForIndex(_ index: Int) -> Movie? {
        // This should be connected to your actual data source
        // For now, return a sample movie
        return Movie.sample
    }
}

// MARK: - Enhanced Views (Wrappers)

struct EnhancedHomeView: View {
    var body: some View {
        HomeView()
    }
}

struct EnhancedSearchView: View {
    var body: some View {
        SearchView()
    }
}

struct EnhancedMovieDetailView: View {
    let movie: Movie

    var body: some View {
        MovieDetailView(movie: movie)
    }
}

struct EnhancedVideoPlayerView: View {
    let episode: Episode
    let movie: Movie

    var body: some View {
        VideoPlayerView(episode: episode, movie: movie)
    }
}

struct EnhancedSettingsView: View {
    var body: some View {
        SettingsView()
    }
}

// MARK: - Transition Overlay

struct TransitionOverlay: View {
    var body: some View {
        Rectangle()
            .fill(Color.black.opacity(0.3))
            .ignoresSafeArea()
            .overlay(
                ProgressView()
                    .scaleEffect(2.0)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            )
    }
}

#Preview {
    ContentView()
}
