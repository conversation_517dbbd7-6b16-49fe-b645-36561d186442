//
//  Movie.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation

// MARK: - Movie Models

struct Movie: Codable, Identifiable, Hashable {
    let id: Int
    let title: String
    let poster: String?
    let year: String?
    let area: String?
    let type: String?
    let remarks: String?
    let director: String?
    let actor: String?
    let source: String
    let sourceName: String
    
    // For detail view
    let description: String?
    let episodes: [Episode]?
    
    init(id: Int, title: String, poster: String? = nil, year: String? = nil,
         area: String? = nil, type: String? = nil, remarks: String? = nil,
         director: String? = nil, actor: String? = nil, source: String, 
         sourceName: String, description: String? = nil, episodes: [Episode]? = nil) {
        self.id = id
        self.title = title
        self.poster = poster
        self.year = year
        self.area = area
        self.type = type
        self.remarks = remarks
        self.director = director
        self.actor = actor
        self.source = source
        self.sourceName = sourceName
        self.description = description
        self.episodes = episodes
    }
    
    var displayYear: String {
        year ?? "Unknown"
    }
    
    var displayArea: String {
        area ?? "Unknown"
    }
    
    var displayType: String {
        type ?? "Movie"
    }
    
    var hasEpisodes: Bool {
        episodes?.isEmpty == false
    }
    
    var firstEpisodeURL: String? {
        episodes?.first?.url
    }
}

struct Episode: Codable, Identifiable, Hashable {
    let id = UUID()
    let episode: Int
    let title: String
    let url: String
    
    private enum CodingKeys: String, CodingKey {
        case episode, title, url
    }
    
    var displayTitle: String {
        if title.isEmpty || title == "Episode \(episode)" {
            return "第\(episode)集"
        }
        return title
    }
}

struct Category: Codable, Identifiable, Hashable {
    let id: Int
    let name: String
    let type: String
    let categoryType: CategoryType
    let color: String?

    init(id: Int, name: String, type: String, categoryType: CategoryType = .content, color: String? = nil) {
        self.id = id
        self.name = name
        self.type = type
        self.categoryType = categoryType
        self.color = color
    }
}

enum CategoryType: String, Codable, CaseIterable {
    case content = "content"        // 电影、电视剧
    case trending = "trending"      // 热门、最新、经典
    case rating = "rating"          // 豆瓣高分、冷门佳片
    case region = "region"          // 华语、欧美、韩国、日本
    case genre = "genre"            // 动作、喜剧、爱情、科幻、悬疑、恐怖、治愈

    var displayName: String {
        switch self {
        case .content: return "内容类型"
        case .trending: return "热门推荐"
        case .rating: return "评分精选"
        case .region: return "地区分类"
        case .genre: return "类型分类"
        }
    }
}

// MARK: - Category Extensions

extension Category {
    static let webCategories: [Category] = [
        // Content Types (电影、电视剧)
        Category(id: 1, name: "电影", type: "1", categoryType: .content, color: "#FF6B6B"),
        Category(id: 2, name: "电视剧", type: "2", categoryType: .content, color: "#4ECDC4"),

        // Trending Categories (热门推荐)
        Category(id: 10, name: "热门", type: "hot", categoryType: .trending, color: "#FF6B6B"),
        Category(id: 11, name: "最新", type: "latest", categoryType: .trending, color: "#45B7D1"),
        Category(id: 12, name: "经典", type: "classic", categoryType: .trending, color: "#96CEB4"),
        Category(id: 13, name: "豆瓣高分", type: "douban_high", categoryType: .rating, color: "#FFEAA7"),
        Category(id: 14, name: "冷门佳片", type: "hidden_gems", categoryType: .rating, color: "#DDA0DD"),

        // Region Categories (地区分类)
        Category(id: 20, name: "华语", type: "chinese", categoryType: .region, color: "#FF7675"),
        Category(id: 21, name: "欧美", type: "western", categoryType: .region, color: "#74B9FF"),
        Category(id: 22, name: "韩国", type: "korean", categoryType: .region, color: "#FD79A8"),
        Category(id: 23, name: "日本", type: "japanese", categoryType: .region, color: "#FDCB6E"),

        // Genre Categories (类型分类)
        Category(id: 30, name: "动作", type: "action", categoryType: .genre, color: "#E17055"),
        Category(id: 31, name: "喜剧", type: "comedy", categoryType: .genre, color: "#00B894"),
        Category(id: 32, name: "爱情", type: "romance", categoryType: .genre, color: "#E84393"),
        Category(id: 33, name: "科幻", type: "scifi", categoryType: .genre, color: "#0984E3"),
        Category(id: 34, name: "悬疑", type: "mystery", categoryType: .genre, color: "#6C5CE7"),
        Category(id: 35, name: "恐怖", type: "horror", categoryType: .genre, color: "#2D3436"),
        Category(id: 36, name: "治愈", type: "healing", categoryType: .genre, color: "#00CEC9")
    ]

    static let movieCategories = webCategories.filter { $0.type == "1" || $0.categoryType != .content }
    static let tvCategories = webCategories.filter { $0.type == "2" || $0.categoryType != .content }
}

// MARK: - API Response Models

struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let error: String?
}

struct SearchResponse: Codable {
    let success: Bool
    let data: [Movie]
    let error: String?
}

struct VideoSource: Codable {
    let key: String
    let name: String
    let adult: Bool
}

struct SourcesResponse: Codable {
    let success: Bool
    let data: [VideoSource]
    let error: String?
}

struct MovieDetailResponse: Codable {
    let success: Bool
    let data: Movie?
    let error: String?
}

struct RecommendationsResponse: Codable {
    let success: Bool
    let data: [Movie]
    let error: String?
}

struct CategoriesResponse: Codable {
    let success: Bool
    let data: [Category]
    let error: String?
}

// MARK: - Extensions

extension Movie {
    static let sample = Movie(
        id: 1,
        title: "Sample Movie",
        poster: "https://example.com/poster.jpg",
        year: "2024",
        area: "美国",
        type: "动作片",
        remarks: "HD",
        director: "Sample Director",
        actor: "Sample Actor",
        source: "sample",
        sourceName: "Sample Source",
        description: "This is a sample movie description for testing purposes.",
        episodes: [
            Episode(episode: 1, title: "Episode 1", url: "https://cdn.ryplay11.com/20250713/181506_66de37e2/index.m3u8"),
            Episode(episode: 2, title: "Episode 2", url: "https://cdn.ryplay11.com/20250713/181505_89c6d4e8/index.m3u8")
        ]
    )
    
    static let sampleList = [
        Movie(id: 1, title: "Action Movie", poster: nil, year: "2024", area: "美国", type: "动作片", remarks: "HD", source: "sample", sourceName: "Sample"),
        Movie(id: 1, title: "Comedy Movie", poster: nil, year: "2023", area: "美国", type: "喜剧片", remarks: "HD", source: "sample", sourceName: "Sample"),
        Movie(id: 1, title: "Drama Series", poster: nil, year: "2024", area: "韩国", type: "电视剧", remarks: "更新至10集", source: "sample", sourceName: "Sample")
    ]
}
