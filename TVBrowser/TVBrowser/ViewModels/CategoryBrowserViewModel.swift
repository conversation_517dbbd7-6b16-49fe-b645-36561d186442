//
//  CategoryBrowserViewModel.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import Combine

class CategoryBrowserViewModel: ObservableObject {
    @Published var categories: [Category] = []
    @Published var selectedContentType: Category?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let apiService: APIService
    private var cancellables = Set<AnyCancellable>()
    
    init(apiService: APIService = APIService.shared) {
        self.apiService = apiService
    }
    
    var contentTypes: [Category] {
        categories.filter { $0.categoryType == .content }
    }
    
    var filteredCategories: [Category] {
        guard let selectedContentType = selectedContentType else {
            return categories.filter { $0.categoryType != .content }
        }
        
        // Return all non-content categories when a content type is selected
        return categories.filter { $0.categoryType != .content }
    }
    
    func loadCategories() {
        isLoading = true
        errorMessage = nil
        
        apiService.fetchCategories()
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            self?.errorMessage = error.localizedDescription
                        }
                    }
                },
                receiveValue: { [weak self] categories in
                    DispatchQueue.main.async {
                        self?.categories = categories
                        // Auto-select first content type (电影)
                        self?.selectedContentType = categories.first { $0.categoryType == .content }
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func selectContentType(_ contentType: Category) {
        selectedContentType = contentType
    }
    
    func refresh() {
        loadCategories()
    }
}
