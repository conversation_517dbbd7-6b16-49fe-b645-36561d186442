//
//  HomeViewModel.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import Combine

class HomeViewModel: ObservableObject {
    @Published var recommendations: [Movie] = []
    @Published var categories: [Category] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let apiService: APIService
    private var cancellables = Set<AnyCancellable>()
    
    init(apiService: APIService = APIService.shared) {
        self.apiService = apiService
        loadData()
    }
    
    func loadData() {
        isLoading = true
        errorMessage = nil
        
        // Load recommendations and categories simultaneously
        Publishers.Zip(
            apiService.fetchRecommendations(),
            apiService.fetchCategories()
        )
        .sink(
            receiveCompletion: { [weak self] completion in
                DispatchQueue.main.async {
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.errorMessage = error.localizedDescription
                    }
                }
            },
            receiveValue: { [weak self] recommendations, categories in
                DispatchQueue.main.async {
                    self?.recommendations = recommendations
                    self?.categories = categories
                }
            }
        )
        .store(in: &cancellables)
    }
    
    func refresh() {
        loadData()
    }
}

class SearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchResults: [Movie] = []
    @Published var isSearching = false
    @Published var errorMessage: String?
    @Published var availableSources: [(String, String)] = []

    private let apiService: APIService
    private var cancellables = Set<AnyCancellable>()
    private var searchCancellable: AnyCancellable?
    private let settings = SettingsManager.shared

    init(apiService: APIService = APIService.shared) {
        self.apiService = apiService
        setupSearchDebouncing()
        loadAvailableSources()

        settings.$adultContentEnabled
            .sink { [weak self] _ in
                self?.loadAvailableSources()
            }
            .store(in: &cancellables)
    }
    
    private func setupSearchDebouncing() {
        $searchText
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] searchText in
                if !searchText.isEmpty {
                    self?.performSearch(query: searchText)
                } else {
                    self?.searchResults = []
                }
            }
            .store(in: &cancellables)
    }
    
    func loadAvailableSources() {
        apiService.fetchAvailableSources(includeAdult: settings.adultContentEnabled)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to load sources: \(error)")
                    }
                },
                receiveValue: { [weak self] sources in
                    DispatchQueue.main.async {
                        self?.availableSources = sources.map { ($0.key, $0.name) }
                    }
                }
            )
            .store(in: &cancellables)
    }

    func performSearch(query: String) {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            searchResults = []
            return
        }

        isSearching = true
        errorMessage = nil

        searchCancellable?.cancel()
        
        searchCancellable = apiService.searchMoviesAggregated(
            query: query,
            includeAdult: settings.adultContentEnabled
        )
        .sink(
            receiveCompletion: { [weak self] completion in
                DispatchQueue.main.async {
                    self?.isSearching = false
                    if case .failure(let error) = completion {
                        self?.errorMessage = error.localizedDescription
                    }
                }
            },
            receiveValue: { [weak self] movies in
                DispatchQueue.main.async {
                    self?.searchResults = movies
                }
            }
        )
    }
    
    func clearSearch() {
        searchText = ""
        searchResults = []
        errorMessage = nil
    }
    
    func changeSource(_ source: String) {
        if !searchText.isEmpty {
            performSearch(query: searchText)
        }
    }
}

class MovieDetailViewModel: ObservableObject {
    @Published var movie: Movie?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let apiService: APIService
    private var cancellables = Set<AnyCancellable>()
    
    init(apiService: APIService = APIService.shared) {
        self.apiService = apiService
    }
    
    func loadMovieDetail(id: String, source: String) {
        isLoading = true
        errorMessage = nil
        
        apiService.fetchMovieDetail(id: id, source: source)
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        self?.isLoading = false
                        if case .failure(let error) = completion {
                            self?.errorMessage = error.localizedDescription
                        }
                    }
                },
                receiveValue: { [weak self] movie in
                    DispatchQueue.main.async {
                        self?.movie = movie
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func refresh(id: String, source: String) {
        loadMovieDetail(id: id, source: source)
    }
}
