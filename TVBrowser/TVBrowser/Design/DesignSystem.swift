//
//  DesignSystem.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

// MARK: - Design System

struct DesignSystem {
    
    // MARK: - Colors
    
    struct Colors {
        // Primary Colors
        static let primary = Color.white
        static let secondary = Color.gray
        static let accent = Color.blue
        
        // Background Colors
        static let background = Color.black
        static let backgroundSecondary = Color.black.opacity(0.95)
        static let backgroundTertiary = Color.black.opacity(0.85)
        
        // Surface Colors
        static let surface = Color.white.opacity(0.1)
        static let surfaceSecondary = Color.white.opacity(0.05)
        static let surfaceHover = Color.white.opacity(0.15)
        
        // Text Colors
        static let textPrimary = Color.white
        static let textSecondary = Color.gray
        static let textTertiary = Color.gray.opacity(0.7)
        
        // Status Colors
        static let success = Color.green
        static let warning = Color.orange
        static let error = Color.red
        static let info = Color.blue
        
        // Focus Colors
        static let focusRing = Color.white
        static let focusBackground = Color.white.opacity(0.2)
        
        // Gradient Colors
        static let gradientPrimary = Linear<PERSON><PERSON>ient(
            colors: [Color.black, Color.black.opacity(0.8), Color.black],
            startPoint: .top,
            endPoint: .bottom
        )
        
        static let gradientOverlay = LinearGradient(
            colors: [Color.clear, Color.black.opacity(0.7)],
            startPoint: .top,
            endPoint: .bottom
        )
        
        static let gradientFocus = LinearGradient(
            colors: [Color.blue.opacity(0.3), Color.purple.opacity(0.3)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    // MARK: - Typography
    
    struct Typography {
        // Display Fonts
        static let displayLarge = Font.system(size: 48, weight: .bold, design: .rounded)
        static let displayMedium = Font.system(size: 36, weight: .bold, design: .rounded)
        static let displaySmall = Font.system(size: 28, weight: .bold, design: .rounded)
        
        // Headline Fonts
        static let headlineLarge = Font.system(size: 32, weight: .semibold)
        static let headlineMedium = Font.system(size: 24, weight: .semibold)
        static let headlineSmall = Font.system(size: 20, weight: .semibold)
        
        // Title Fonts
        static let titleLarge = Font.system(size: 22, weight: .medium)
        static let titleMedium = Font.system(size: 18, weight: .medium)
        static let titleSmall = Font.system(size: 16, weight: .medium)
        
        // Body Fonts
        static let bodyLarge = Font.system(size: 20, weight: .regular)
        static let bodyMedium = Font.system(size: 18, weight: .regular)
        static let bodySmall = Font.system(size: 16, weight: .regular)
        
        // Label Fonts
        static let labelLarge = Font.system(size: 20, weight: .medium)
        static let labelMedium = Font.system(size: 18, weight: .medium)
        static let labelSmall = Font.system(size: 16, weight: .medium)
        
        // Caption Fonts
        static let caption = Font.system(size: 12, weight: .regular)
        static let captionSmall = Font.system(size: 10, weight: .regular)
    }
    
    // MARK: - Spacing
    
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
        
        // TV-specific spacing
        static let tvSafeArea: CGFloat = 60
        static let tvCardSpacing: CGFloat = 30
        static let tvSectionSpacing: CGFloat = 40
    }
    
    // MARK: - Corner Radius
    
    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        
        // TV-specific radius
        static let tvCard: CGFloat = 12
        static let tvButton: CGFloat = 8
        static let tvModal: CGFloat = 16
    }
    
    // MARK: - Shadows
    
    struct Shadows {
        static let small = (color: Color.black.opacity(0.3), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4))
        static let medium = (color: Color.black.opacity(0.4), radius: CGFloat(16), x: CGFloat(0), y: CGFloat(8))
        static let large = (color: Color.black.opacity(0.5), radius: CGFloat(24), x: CGFloat(0), y: CGFloat(12))
        
        // Focus shadows
        static let focus = (color: Color.white.opacity(0.3), radius: CGFloat(20), x: CGFloat(0), y: CGFloat(0))
        static let focusStrong = (color: Color.white.opacity(0.5), radius: CGFloat(30), x: CGFloat(0), y: CGFloat(0))
    }
    
    // MARK: - Animations
    
    struct Animations {
        static let fast = Animation.easeInOut(duration: 0.2)
        static let medium = Animation.easeInOut(duration: 0.3)
        static let slow = Animation.easeInOut(duration: 0.5)
        
        // Focus animations
        static let focus = Animation.easeInOut(duration: 0.2)
        static let focusSpring = Animation.spring(response: 0.3, dampingFraction: 0.8)
        
        // Transition animations
        static let slideIn = Animation.easeOut(duration: 0.4)
        static let slideOut = Animation.easeIn(duration: 0.3)
        static let fadeIn = Animation.easeIn(duration: 0.3)
        static let fadeOut = Animation.easeOut(duration: 0.2)
        
        // Scale animations
        static let scaleUp = Animation.spring(response: 0.4, dampingFraction: 0.7)
        static let scaleDown = Animation.easeInOut(duration: 0.2)
    }
    
    // MARK: - Sizes
    
    struct Sizes {
        // Card sizes
        static let movieCardWidth: CGFloat = 200
        static let movieCardHeight: CGFloat = 300
        static let categoryCardWidth: CGFloat = 150
        static let categoryCardHeight: CGFloat = 80
        
        // Button sizes
        static let buttonHeight: CGFloat = 60
        static let buttonHeightSmall: CGFloat = 44
        static let iconButtonSize: CGFloat = 50
        
        // Player sizes
        static let progressBarHeight: CGFloat = 4
        static let controlButtonSize: CGFloat = 80
        static let controlButtonSmall: CGFloat = 50
        
        // Focus effects
        static let focusScale: CGFloat = 1.1
        static let focusScaleSmall: CGFloat = 1.05
        static let focusScaleLarge: CGFloat = 1.15
    }
    
    // MARK: - Effects
    
    struct Effects {
        // Blur effects
        static let backgroundBlur = 20.0
        static let overlayBlur = 10.0
        
        // Opacity values
        static let disabled: Double = 0.5
        static let overlay: Double = 0.8
        static let subtle: Double = 0.1
        static let medium: Double = 0.3
        static let strong: Double = 0.7
    }
}

// MARK: - Design System Extensions

extension View {
    // Apply design system colors
    func designSystemBackground() -> some View {
        self.background(DesignSystem.Colors.background)
    }
    
    func designSystemSurface() -> some View {
        self.background(DesignSystem.Colors.surface)
    }
    
    // Apply design system shadows
    func designSystemShadow(_ shadow: (color: Color, radius: CGFloat, x: CGFloat, y: CGFloat)) -> some View {
        self.shadow(color: shadow.color, radius: shadow.radius, x: shadow.x, y: shadow.y)
    }
    
    // Apply focus effects
    func focusEffect(isFocused: Bool, scale: CGFloat = DesignSystem.Sizes.focusScale) -> some View {
        self
            .scaleEffect(isFocused ? scale : 1.0)
            .designSystemShadow(isFocused ? DesignSystem.Shadows.focus : DesignSystem.Shadows.small)
            .animation(DesignSystem.Animations.focus, value: isFocused)
    }
    
    // Apply TV-safe padding
    func tvSafePadding() -> some View {
        self.padding(.horizontal, DesignSystem.Spacing.tvSafeArea)
    }
}
