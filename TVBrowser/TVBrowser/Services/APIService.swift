//
//  APIService.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import Foundation
import Combine

class APIService: ObservableObject {
    static let shared = APIService()
    
    private let baseURL = "http://localhost:8080/api"
    private let session = URLSession.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    // MARK: - API Methods
    
    func fetchRecommendations() -> AnyPublisher<[Movie], APIError> {
        guard let url = URL(string: "\(baseURL)/recommendations") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: RecommendationsResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func searchMovies(query: String, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(baseURL)/search?q=\(encodedQuery)&source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SearchResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func searchMoviesAggregated(query: String, includeAdult: Bool = false) -> AnyPublisher<[Movie], APIError> {
        guard let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: "\(baseURL)/search?q=\(encodedQuery)&aggregated=true&includeAdult=\(includeAdult)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SearchResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func fetchAvailableSources(includeAdult: Bool = false) -> AnyPublisher<[VideoSource], APIError> {
        guard let url = URL(string: "\(baseURL)/sources?includeAdult=\(includeAdult)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: SourcesResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func fetchMovieDetail(id: String, source: String = "heimuer") -> AnyPublisher<Movie, APIError> {
        guard let url = URL(string: "\(baseURL)/movie/\(id)?source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: MovieDetailResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success, let movie = response.data {
                    return movie
                } else {
                    throw APIError.serverError(response.error ?? "Movie not found")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    func fetchCategories() -> AnyPublisher<[Category], APIError> {
        guard let url = URL(string: "\(baseURL)/categories") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: CategoriesResponse.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data
                } else {                    
//                    return Category.webCategories
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    func fetchMoviesByCategory(categoryType: String, page: Int = 1, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        guard let url = URL(string: "\(baseURL)/category/\(categoryType)?page=\(page)&source=\(source)") else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return URLSession.shared.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: APIResponse<[Movie]>.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data ?? []
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }

    // Enhanced category-based fetching with more parameters
    func fetchMoviesByCategory(category: Category, page: Int = 1, source: String = "heimuer", sortBy: String = "latest") -> AnyPublisher<[Movie], APIError> {
        var urlString = "\(baseURL)"

        // Build URL based on category type
        switch category.categoryType {
        case .content:
            urlString += "/category/\(category.type)"
        case .trending:
            urlString += "/trending/\(category.type)"
        case .rating:
            urlString += "/rating/\(category.type)"
        case .region:
            urlString += "/region/\(category.type)"
        case .genre:
            urlString += "/genre/\(category.type)"
        }

        urlString += "?page=\(page)&source=\(source)&sort=\(sortBy)"

        guard let url = URL(string: urlString) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }

        return session.dataTaskPublisher(for: url)
            .map(\.data)
            .decode(type: APIResponse<[Movie]>.self, decoder: JSONDecoder())
            .tryMap { response in
                if response.success {
                    return response.data ?? []
                } else {
                    throw APIError.serverError(response.error ?? "Unknown error")
                }
            }
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else if let apiError = error as? APIError {
                    return apiError
                } else {
                    return APIError.networkError
                }
            }
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
}

// MARK: - API Error

enum APIError: Error, LocalizedError {
    case invalidURL
    case networkError
    case decodingError
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .networkError:
            return "Network connection error"
        case .decodingError:
            return "Failed to decode response"
        case .serverError(let message):
            return message
        }
    }
}

// MARK: - Mock Service for Preview

class MockAPIService: APIService {
    override func fetchRecommendations() -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func searchMovies(query: String, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList.filter { $0.title.localizedCaseInsensitiveContains(query) })
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func searchMoviesAggregated(query: String, includeAdult: Bool = false) -> AnyPublisher<[Movie], APIError> {
        Just(Movie.sampleList.filter { $0.title.localizedCaseInsensitiveContains(query) })
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func fetchAvailableSources(includeAdult: Bool = false) -> AnyPublisher<[VideoSource], APIError> {
        let sources = [
            VideoSource(key: "heimuer", name: "黑木耳", adult: false),
            VideoSource(key: "bfzy", name: "暴风资源", adult: false),
            VideoSource(key: "ruyi", name: "如意资源", adult: false),
            VideoSource(key: "ffzy", name: "非凡影视", adult: false),
            VideoSource(key: "tyyszy", name: "天涯资源", adult: false)
        ]
        return Just(sources)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func fetchMovieDetail(id: String, source: String = "heimuer") -> AnyPublisher<Movie, APIError> {
        Just(Movie.sample)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
    
    override func fetchCategories() -> AnyPublisher<[Category], APIError> {
        return Just(Category.webCategories)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func fetchMoviesByCategory(category: Category, page: Int = 1, source: String = "heimuer", sortBy: String = "latest") -> AnyPublisher<[Movie], APIError> {
        // Simulate different movies for different categories
        var filteredMovies = Movie.sampleList

        // Filter based on category type for demo purposes
        switch category.categoryType {
        case .content:
            // Return all movies for content categories
            break
        case .trending:
            // Return shuffled movies for trending categories
            filteredMovies = Array(Movie.sampleList.shuffled().prefix(10))
        case .rating:
            // Return top-rated movies
            filteredMovies = Array(Movie.sampleList.prefix(8))
        case .region:
            // Filter by region (simplified)
            filteredMovies = Array(Movie.sampleList.shuffled().prefix(12))
        case .genre:
            // Filter by genre (simplified)
            filteredMovies = Array(Movie.sampleList.shuffled().prefix(15))
        }

        return Just(filteredMovies)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }

    override func fetchMoviesByCategory(categoryType: String, page: Int = 1, source: String = "heimuer") -> AnyPublisher<[Movie], APIError> {
        // Return filtered sample movies based on category type
        let filteredMovies = Movie.sampleList.filter { movie in
            switch categoryType {
            case "1": return movie.type?.contains("电影") == true
            case "2": return movie.type?.contains("电视剧") == true || movie.type?.contains("剧集") == true
            case "3": return movie.type?.contains("综艺") == true
            case "4": return movie.type?.contains("动漫") == true || movie.type?.contains("动画") == true
            default: return true
            }
        }

        return Just(filteredMovies)
            .setFailureType(to: APIError.self)
            .eraseToAnyPublisher()
    }
}
