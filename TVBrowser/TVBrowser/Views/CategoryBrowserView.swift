//
//  CategoryBrowserView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import Combine

struct CategoryBrowserView: View {
    @StateObject private var viewModel = CategoryBrowserViewModel()
    @EnvironmentObject private var navigationManager: NavigationManager
    @FocusState private var focusedItem: FocusableCategoryItem?
    
    enum FocusableCategoryItem: Hashable {
        case contentType(Int)
        case category(Int)
        case backButton
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 40) {
                    // Header
                    headerView
                    
                    // Content Type Selector (电影/电视剧)
                    contentTypeSelector
                    
                    // Category Sections
                    if viewModel.isLoading {
                        loadingView
                    } else {
                        categoryGridView
                    }
                }
                .padding(.horizontal, 60)
                .padding(.vertical, 40)
            }
            .background(backgroundView)
            .navigationBarHidden(true)
        }
        .onAppear {
            viewModel.loadCategories()
            setInitialFocus()
        }
    }
    
    private var backgroundView: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            LinearGradient(
                colors: [
                    Color.black,
                    Color.black.opacity(0.98),
                    Color.black.opacity(0.95),
                    Color.black
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        }
    }
    
    private var headerView: some View {
        HStack {
            Button("返回") {
                navigationManager.dismissCategoryView()
            }
            .buttonStyle(TVButtonStyle())
            .font(.system(size: 18, weight: .medium))
            .focused($focusedItem, equals: .backButton)
            
            Spacer()
            
            Text("分类浏览")
                .font(.system(size: 40, weight: .bold))
                .foregroundColor(.white)
            
            Spacer()
        }
    }
    
    private var contentTypeSelector: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("内容类型")
                .font(.system(size: 28, weight: .semibold))
                .foregroundColor(.white)
            
            HStack(spacing: 20) {
                ForEach(Array(viewModel.contentTypes.enumerated()), id: \.element.id) { index, contentType in
                    ContentTypeButton(
                        category: contentType,
                        isSelected: viewModel.selectedContentType?.id == contentType.id,
                        isFocused: focusedItem == .contentType(index)
                    ) {
                        viewModel.selectContentType(contentType)
                    }
                    .focused($focusedItem, equals: .contentType(index))
                }
                Spacer()
            }
        }
    }
    
    private var categoryGridView: some View {
        VStack(alignment: .leading, spacing: 40) {
            ForEach(CategoryType.allCases.filter { $0 != .content }, id: \.self) { categoryType in
                categorySection(for: categoryType)
            }
        }
    }
    
    private func categorySection(for categoryType: CategoryType) -> some View {
        let categories = viewModel.filteredCategories.filter { $0.categoryType == categoryType }
        
        guard !categories.isEmpty else {
            return AnyView(EmptyView())
        }
        
        return AnyView(
            VStack(alignment: .leading, spacing: 20) {
                Text(categoryType.displayName)
                    .font(.system(size: 28, weight: .semibold))
                    .foregroundColor(.white)
                
                LazyVGrid(
                    columns: Array(repeating: GridItem(.flexible(), spacing: 20), count: 6),
                    spacing: 20
                ) {
                    ForEach(Array(categories.enumerated()), id: \.element.id) { index, category in
                        CategoryCard(
                            category: category,
                            isFocused: focusedItem == .category(category.id)
                        ) {
                            navigationManager.presentCategoryView(for: category)
                        }
                        .focused($focusedItem, equals: .category(category.id))
                    }
                }
            }
        )
    }
    
    private var loadingView: some View {
        VStack(spacing: 30) {
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            Text("加载分类中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxHeight: 300)
    }
    
    private func setInitialFocus() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            focusedItem = .contentType(0)
        }
    }
}

// MARK: - Content Type Button

struct ContentTypeButton: View {
    let category: Category
    let isSelected: Bool
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(category.name)
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, 30)
                .padding(.vertical, 15)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            isSelected ? Color.white :
                            isFocused ? Color.white.opacity(0.2) : Color.white.opacity(0.1)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    isFocused ? Color.white.opacity(0.8) : Color.clear,
                                    lineWidth: 2
                                )
                        )
                )
        }
        .buttonStyle(TVButtonStyle())
        .scaleEffect(isFocused ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Category Card

struct CategoryCard: View {
    let category: Category
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Category Icon/Color
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(hex: category.color ?? "#4ECDC4"),
                                Color(hex: category.color ?? "#4ECDC4").opacity(0.7)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 80)
                    .overlay(
                        Text(String(category.name.prefix(1)))
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.white)
                    )
                
                // Category Name
                Text(category.name)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                // Highlight indicator
                if category.isHighlighted {
                    Text("精选")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            Capsule()
                                .fill(Color.pink.opacity(0.8))
                        )
                }
            }
            .frame(width: 140, height: 140)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(isFocused ? 0.15 : 0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                isFocused ? Color.white.opacity(0.8) : Color.clear,
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(TVButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .shadow(
            color: isFocused ? Color.white.opacity(0.3) : Color.clear,
            radius: isFocused ? 15 : 0
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Color Extension

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    CategoryBrowserView()
}
