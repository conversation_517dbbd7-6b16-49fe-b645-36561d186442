//
//  SearchView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import Combine

struct SearchView: View {
    @StateObject private var viewModel = SearchViewModel()
    @EnvironmentObject private var navigationManager: NavigationManager
    @FocusState private var focusedItem: FocusableSearchItem?
    
    enum FocusableSearchItem: Hashable {
        case searchField
        case sourceButton(String)
        case searchResult(Int)
        case clearButton
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header with Search
                headerView
                                
                // Search Results
                contentView
                
                Spacer()
            }
            .padding(.horizontal, 60)
            .padding(.vertical, 40)
            .background(backgroundView)
            .navigationBarHidden(true)
        }
        .onAppear {
            setInitialFocus()
        }
    }
    
    private var backgroundView: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            LinearGradient(
                colors: [
                    Color.black,
                    Color.black.opacity(0.98),
                    Color.black.opacity(0.95),
                    Color.black
                ],
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()
        }
    }
    
    private var contentView: some View {
        Group {
            if viewModel.isSearching {
                loadingView
            } else if let errorMessage = viewModel.errorMessage {
                errorView(errorMessage)
            } else if !viewModel.searchResults.isEmpty {
                searchResultsView
            } else if !viewModel.searchText.isEmpty {
                noResultsView
            } else {
                searchPromptView
            }
        }
    }
    
    private func setInitialFocus() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            focusedItem = .searchField
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 20) {
            HStack {
                Text("搜索影片")
                    .font(.system(size: 40, weight: .bold))
                    .foregroundColor(.white)
                
                Spacer()
                
                if !viewModel.searchText.isEmpty {
                    clearButton
                }
            }
            
            searchField
        }
    }
    
    private var clearButton: some View {
        Button("清除") {
            viewModel.clearSearch()
            focusedItem = .searchField
        }
        .buttonStyle(TVButtonStyle())
        .font(.system(size: 18, weight: .medium))
        .focused($focusedItem, equals: .clearButton)
        .scaleEffect(focusedItem == .clearButton ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .clearButton)
    }
    
    private var searchField: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 24))
                .foregroundColor(.gray)
            
            TextField("输入影片名称...", text: $viewModel.searchText)
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(focusedItem == .searchField ? .black : .white)
                .focused($focusedItem, equals: .searchField)
                .onSubmit {
                    if !viewModel.searchText.isEmpty {
                        viewModel.performSearch(query: viewModel.searchText)
                    }
                }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 15)
        .background(searchFieldBackground)
        .scaleEffect(focusedItem == .searchField ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .searchField)
    }
    
    private var searchFieldBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.white.opacity(focusedItem == .searchField ? 0.15 : 0.1))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        focusedItem == .searchField ? Color.white.opacity(0.8) : Color.white.opacity(0.3),
                        lineWidth: focusedItem == .searchField ? 2 : 1
                    )
            )
    }
 
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 30) {
            // Enhanced loading animation
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            Text("加载中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxHeight: .infinity, alignment: .center) // Vertically center
        .frame(maxWidth: .infinity)
        .frame(minHeight: 400, alignment: .center)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 30) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("搜索失败")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text(message)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(3)
            
            Button("重试") {
                viewModel.performSearch(query: viewModel.searchText)
            }
            .buttonStyle(TVButtonStyle())
            .font(.system(size: 20, weight: .medium))
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Search Results View
    
    private var searchResultsView: some View {
        VStack(alignment: .leading, spacing: 20) {
            Text("搜索结果 (\(viewModel.searchResults.count))")
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
            
            ScrollView {
                searchResultsGrid
            }
        }
    }
    
    private var searchResultsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 30), count: 5), spacing: 30) {
            ForEach(Array(viewModel.searchResults.enumerated()), id: \.element.id) { index, movie in
                searchResultCard(movie: movie, index: index)
            }
        }
        .padding(.horizontal, 60)
    }
    
    private func searchResultCard(movie: Movie, index: Int) -> some View {
        FocusableSearchResultCard(
            movie: movie,
            index: index,
            isFocused: focusedItem == .searchResult(index)
        ) {
            print("🎬 Search result tapped: \(movie.title)")
            navigationManager.presentMovieDetail(for: movie)
        }
        .focused($focusedItem, equals: .searchResult(index))
    }
    
    // MARK: - No Results View
    
    private var noResultsView: some View {
        VStack(spacing: 30) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("未找到相关影片")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text("尝试使用不同的关键词或更换数据源")
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(maxHeight: 300)
    }
    
    // MARK: - Search Prompt View
    
    private var searchPromptView: some View {
        VStack(spacing: 30) {
            Image(systemName: "tv")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("开始搜索")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text("输入影片名称来搜索您想观看的内容")
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .frame(maxHeight: 300)
    }
}

struct FocusableSearchResultCard: View {
    let movie: Movie
    let index: Int
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                posterView
                movieInfo
            }
        }
        .buttonStyle(TVButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
    
    private var posterView: some View {
        AsyncImage(url: URL(string: movie.poster ?? "")) { image in
            image
                .resizable()
                .aspectRatio(2/3, contentMode: .fill)
        } placeholder: {
            posterPlaceholder
        }
        .frame(width: 200, height: 300)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .overlay(focusBorder)
        .shadow(
            color: isFocused ? Color.white.opacity(0.3) : Color.black.opacity(0.3),
            radius: isFocused ? 15 : 5,
            x: 0,
            y: isFocused ? 6 : 2
        )
    }
    
    private var posterPlaceholder: some View {
        Rectangle()
            .fill(Color.gray.opacity(0.3))
            .aspectRatio(2/3, contentMode: .fill)
            .overlay(
                VStack(spacing: 8) {
                    Image(systemName: "photo")
                        .font(.system(size: 30))
                        .foregroundColor(.gray)
                    
                    loadingDots
                }
            )
    }
    
    private var loadingDots: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { dotIndex in
                Circle()
                    .fill(Color.gray)
                    .frame(width: 6, height: 6)
                    .scaleEffect(dotIndex == 0 ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.6).repeatForever().delay(Double(dotIndex) * 0.2), value: dotIndex)
            }
        }
    }
    
    private var focusBorder: some View {
        RoundedRectangle(cornerRadius: 8)
            .stroke(Color.white.opacity(isFocused ? 0.8 : 0), lineWidth: 3)
    }
    
    private var movieInfo: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(movie.title)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            Text(movie.displayYear)
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.gray)
                .lineLimit(1)
        }
        .frame(width: 160, alignment: .leading)
    }
}

#Preview {
    SearchView()
}
