//
//  CategoryView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI
import Combine

// MARK: - Category View

struct CategoryView: View {
    let category: Category
    
    @StateObject private var viewModel: CategoryViewModel
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    
    // Focus management
    @FocusState private var focusedItem: FocusableCategoryItem?
    
    enum FocusableCategoryItem: Hashable {
        case sourceButton(String)
        case movie(Int)
        case loadMoreButton
    }
    
    init(category: Category) {
        self.category = category
        self._viewModel = StateObject(wrappedValue: CategoryViewModel(category: category))
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.tvSectionSpacing) {
                // Header
                headerView
                
                // Movies Grid
                if viewModel.isLoading && viewModel.movies.isEmpty {
                    loadingView
                } else if viewModel.movies.isEmpty {
                    emptyStateView
                } else {
                    moviesGridView
                }
                
                // Load More Button
                if viewModel.hasMorePages && !viewModel.isLoading {
                    loadMoreButton
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.tvSafeArea)
            .padding(.vertical, DesignSystem.Spacing.tvSectionSpacing)
        }
        .background(
            // Enhanced background
            ZStack {
                Color.black.ignoresSafeArea()
                
                LinearGradient(
                    colors: [
                        Color.black,
                        Color.black.opacity(0.98),
                        Color.black.opacity(0.95),
                        Color.black
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
            }
        )
        .onAppear {
            viewModel.loadMovies()
            
            // Set initial focus
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                setInitialFocus()
            }
        }
    }
    
    // MARK: - Focus Management
    
    private func setInitialFocus() {
        if !viewModel.movies.isEmpty {
            focusedItem = .movie(0)
        } else {
            focusedItem = .sourceButton("heimuer")
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                Text(category.name)
                    .font(DesignSystem.Typography.displayMedium)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                if !viewModel.movies.isEmpty {
                    Text("共 \(viewModel.totalMovies) 部影片")
                        .font(DesignSystem.Typography.bodyLarge)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Movies Grid View
    
    private var moviesGridView: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: DesignSystem.Spacing.tvCardSpacing), count: 6),
            spacing: DesignSystem.Spacing.tvCardSpacing
        ) {
            ForEach(Array(viewModel.movies.enumerated()), id: \.element.id) { index, movie in
                FocusableCategoryMovieCard(
                    movie: movie,
                    index: index,
                    isFocused: focusedItem == .movie(index)
                ) {
                    navigationManager.presentMovieDetail(for: movie)
                }
                .focused($focusedItem, equals: .movie(index))
            }
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 30) {
            // Enhanced loading animation
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            Text("加载中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxHeight: .infinity, alignment: .center) // Vertically center
        .frame(maxWidth: .infinity)
        .frame(minHeight: 400, alignment: .center)
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: DesignSystem.Spacing.xl) {
            Image(systemName: "tv.slash")
                .font(.system(size: 60, weight: .light))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            VStack(spacing: DesignSystem.Spacing.md) {
                Text("暂无\(category.name)")
                    .font(DesignSystem.Typography.displaySmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text("请尝试更换数据源或稍后再试")
                    .font(DesignSystem.Typography.bodyLarge)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 400)
    }
    
    // MARK: - Load More Button
    
    private var loadMoreButton: some View {
        Button(action: {
            viewModel.loadMoreMovies()
        }) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if viewModel.isLoadingMore {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.textPrimary))
                } else {
                    Image(systemName: "arrow.down.circle")
                        .font(.system(size: 20, weight: .medium))
                }
                
                Text(viewModel.isLoadingMore ? "加载中..." : "加载更多")
                    .font(DesignSystem.Typography.titleMedium)
            }
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .padding(.horizontal, DesignSystem.Spacing.xl)
            .padding(.vertical, DesignSystem.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                    .fill(DesignSystem.Colors.surface)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.md)
                            .stroke(
                                focusedItem == .loadMoreButton ? 
                                Color.white.opacity(0.8) : Color.clear,
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(TVButtonStyle())
        .focused($focusedItem, equals: .loadMoreButton)
        .scaleEffect(focusedItem == .loadMoreButton ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .loadMoreButton)
        .disabled(viewModel.isLoadingMore)
    }
    
    // MARK: - Helper Methods
    
    private func getSourceName(_ source: String) -> String {
        switch source {
        case "heimuer": return "黑木耳"
        case "bfzy": return "暴风资源"
        case "ruyi": return "如意资源"
        default: return source
        }
    }
}

// MARK: - Focusable Category Movie Card

struct FocusableCategoryMovieCard: View {
    let movie: Movie
    let index: Int
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster with enhanced focus effects
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            VStack(spacing: 8) {
                                Image(systemName: "photo")
                                    .font(.system(size: 30))
                                    .foregroundColor(.gray)
                                
                                // Loading dots
                                HStack(spacing: 4) {
                                    ForEach(0..<3) { index in
                                        Circle()
                                            .fill(Color.gray)
                                            .frame(width: 6, height: 6)
                                            .scaleEffect(index == 0 ? 1.2 : 1.0)
                                            .animation(.easeInOut(duration: 0.6).repeatForever().delay(Double(index) * 0.2), value: index)
                                    }
                                }
                            }
                        )
                }
                .frame(width: 160, height: 240)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .overlay(
                    // Focus border
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.white.opacity(isFocused ? 0.8 : 0), lineWidth: 3)
                )
                .shadow(
                    color: isFocused ? Color.white.opacity(0.3) : Color.black.opacity(0.3),
                    radius: isFocused ? 15 : 5,
                    x: 0,
                    y: isFocused ? 6 : 2
                )
                
                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                    
                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 160, alignment: .leading)
            }
        }
        .buttonStyle(TVButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Category View Model (unchanged)

class CategoryViewModel: ObservableObject {
    @Published var movies: [Movie] = []
    @Published var isLoading = false
    @Published var isLoadingMore = false
    @Published var hasMorePages = true
    @Published var currentSource = "heimuer"
    @Published var totalMovies = 0
    
    private let category: Category
    private var currentPage = 1
    private var cancellables = Set<AnyCancellable>()
    private let apiService = APIService.shared
    
    init(category: Category) {
        self.category = category
    }
    
    func loadMovies() {
        guard !isLoading else { return }

        isLoading = true
        currentPage = 1
        movies.removeAll()

        // Use the enhanced category API method
        apiService.fetchMoviesByCategory(
            category: category,
            page: currentPage,
            source: currentSource,
            sortBy: "latest"
        )
        .sink(
            receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                DispatchQueue.main.async {
                    self?.isLoading = false
                }
                if case .failure(let error) = completion {
                    print("Failed to load movies: \(error)")
                }
            },
            receiveValue: { [weak self] (movies: [Movie]) in
                DispatchQueue.main.async {
                    self?.movies = movies
                    self?.totalMovies = movies.count
                    self?.hasMorePages = movies.count >= 20 // Assume more pages if we got a full page
                    self?.isLoading = false
                }
            }
        )
        .store(in: &cancellables)
    }
    
    func loadMoreMovies() {
        guard !isLoadingMore && hasMorePages else { return }

        isLoadingMore = true
        currentPage += 1

        apiService.fetchMoviesByCategory(
            category: category,
            page: currentPage,
            source: currentSource,
            sortBy: "latest"
        )
        .sink(
            receiveCompletion: { [weak self] (completion: Subscribers.Completion<APIError>) in
                DispatchQueue.main.async {
                    self?.isLoadingMore = false
                }
                if case .failure(let error) = completion {
                    print("Failed to load more movies: \(error)")
                    self?.currentPage -= 1 // Revert page increment on error
                }
            },
            receiveValue: { [weak self] (newMovies: [Movie]) in
                DispatchQueue.main.async {
                    self?.movies.append(contentsOf: newMovies)
                    self?.totalMovies += newMovies.count
                    self?.hasMorePages = newMovies.count >= 20
                    self?.isLoadingMore = false
                }
            }
        )
        .store(in: &cancellables)
    }
    
    func changeSource(_ newSource: String) {
        guard newSource != currentSource else { return }
        currentSource = newSource
        loadMovies()
    }
}

// MARK: - Preview

#Preview {
    CategoryView(category: Category(id: 1, name: "电影", type: "1", categoryType: .content))
}
