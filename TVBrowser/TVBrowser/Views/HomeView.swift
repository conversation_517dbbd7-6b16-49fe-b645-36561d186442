//
//  HomeView.swift
//  TVBrowser
//
//  Created by <PERSON> on 2025/7/24.
//

import SwiftUI

struct HomeView: View {
    @StateObject private var viewModel = HomeViewModel()
    @Environment(\.navigationManager) private var navigationManager
    @Environment(\.focusManager) private var focusManager
    @State private var selectedMovie: Movie?
    @State private var showingMovieDetail = false
    
    // Focus management
    @FocusState private var focusedItem: FocusableHomeItem?
    
    enum FocusableHomeItem: Hashable {
        case refreshButton
        case movie(section: String, index: Int)
        case category(Int)
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 40) {
                    // Header
                    headerView
                    
                    if viewModel.isLoading {
                        loadingView
                    } else if let errorMessage = viewModel.errorMessage {
                        errorView(errorMessage)
                    } else {
                        // Featured Movies Section
                        if !viewModel.recommendations.isEmpty {
                            movieSection(
                                title: "推荐影片",
                                movies: Array(viewModel.recommendations.prefix(10)),
                                sectionId: "featured"
                            )
                        }
                        
                        // Categories Section
                        if !viewModel.categories.isEmpty {
                            categoriesSection
                        }
                        
                        // Recent Movies Section
                        if viewModel.recommendations.count > 10 {
                            movieSection(
                                title: "最新上映",
                                movies: Array(viewModel.recommendations.dropFirst(10).prefix(10)),
                                sectionId: "recent"
                            )
                        }
                    }
                }
                .tvSafePadding()
                .padding(.vertical, DesignSystem.Spacing.tvSectionSpacing)
            }
            .background(
                // Enhanced background with better gradient
                ZStack {
                    // Base black background
                    Color.black.ignoresSafeArea()
                    
                    // Subtle gradient overlay
                    LinearGradient(
                        colors: [
                            Color.black,
                            Color.black.opacity(0.98),
                            Color.black.opacity(0.95),
                            Color.black
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea()
                }
            )
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingMovieDetail) {
            if let movie = selectedMovie {
                MovieDetailView(movie: movie)
            }
        }
        .onAppear {
            if viewModel.recommendations.isEmpty {
                viewModel.loadData()
            }
            
            // Set initial focus to first movie
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                setInitialFocus()
            }
        }
    }
    
    // MARK: - Focus Management
    
    private func setInitialFocus() {
        if !viewModel.recommendations.isEmpty {
            focusedItem = .movie(section: "featured", index: 0)
        } else if !viewModel.categories.isEmpty {
            focusedItem = .category(0)
        } else {
            focusedItem = .refreshButton
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                Text("LibreTV")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                
                Text("发现精彩影视内容")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: {
                viewModel.refresh()
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(focusedItem == .refreshButton ? 0.2 : 0.1))
                            .overlay(
                                Circle()
                                    .stroke(Color.white.opacity(focusedItem == .refreshButton ? 0.5 : 0), lineWidth: 2)
                            )
                    )
            }
            .buttonStyle(TVButtonStyle())
            .focused($focusedItem, equals: .refreshButton)
            .scaleEffect(focusedItem == .refreshButton ? 1.1 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: focusedItem == .refreshButton)
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 30) {
            // Enhanced loading animation
            ProgressView()
                .scaleEffect(2.0)
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            Text("加载中...")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.gray)
        }
        .frame(maxHeight: .infinity, alignment: .center) // Vertically center
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Error View
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 30) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.red)
            
            Text("加载失败")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text(message)
                .font(.system(size: 20))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(3)
            
            Button("重试") {
                viewModel.refresh()
            }
            .buttonStyle(TVButtonStyle())
            .font(.system(size: 20, weight: .medium))
        }
        .frame(maxWidth: .infinity, maxHeight: 400)
    }
    
    // MARK: - Movie Section
    
    private func movieSection(title: String, movies: [Movie], sectionId: String) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            Text(title)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 30) {
                    ForEach(Array(movies.enumerated()), id: \.element.id) { index, movie in
                        FocusableMovieCard(
                            movie: movie,
                            index: index,
                            sectionId: sectionId,
                            isFocused: focusedItem == .movie(section: sectionId, index: index)
                        ) {
                            selectedMovie = movie
                            showingMovieDetail = true
                        }
                        .focused($focusedItem, equals: .movie(section: sectionId, index: index))
                    }
                }
                .padding(.horizontal, 0)
            }
        }
    }
    
    // MARK: - Categories Section

    private var categoriesSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Text("分类")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)

                Spacer()

                Button("浏览全部分类") {
                    // Present the comprehensive category browser
                    navigationManager.presentCategoryBrowser()
                }
                .buttonStyle(TVButtonStyle())
                .font(.system(size: 18, weight: .medium))
            }

            HStack(spacing: DesignSystem.Spacing.tvCardSpacing) {
                // Show only main content categories
                ForEach(Array(viewModel.categories.filter { $0.categoryType == .content }.enumerated()), id: \.element.id) { index, category in
                    FocusableCategoryCard(
                        category: category,
                        index: index,
                        isFocused: focusedItem == .category(index)
                    ) {
                        navigationManager.presentCategoryView(for: category)
                    }
                    .focused($focusedItem, equals: .category(index))
                }

                // Add trending categories
                ForEach(Array(viewModel.categories.filter { $0.categoryType == .trending }.prefix(3).enumerated()), id: \.element.id) { index, category in
                    FocusableCategoryCard(
                        category: category,
                        index: index + 10, // Offset to avoid focus conflicts
                        isFocused: focusedItem == .category(index + 10)
                    ) {
                        navigationManager.presentCategoryView(for: category)
                    }
                    .focused($focusedItem, equals: .category(index + 10))
                }

                Spacer()
            }
        }
    }
}

// MARK: - Focusable Movie Card

struct FocusableMovieCard: View {
    let movie: Movie
    let index: Int
    let sectionId: String
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Poster with enhanced focus effects
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 200, height: 300)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .overlay(
                    // Focus border
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(isFocused ? 0.8 : 0), lineWidth: 3)
                )
                .shadow(
                    color: isFocused ? Color.white.opacity(0.3) : Color.black.opacity(0.5),
                    radius: isFocused ? 20 : 10,
                    x: 0,
                    y: isFocused ? 8 : 4
                )
                
                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                    
                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 200, alignment: .leading)
            }
        }
        .buttonStyle(TVButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Focusable Category Card

struct FocusableCategoryCard: View {
    let category: Category
    let index: Int
    let isFocused: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(category.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 150, height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.blue.opacity(isFocused ? 0.9 : 0.6),
                                    Color.blue.opacity(isFocused ? 0.7 : 0.4)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(isFocused ? 0.6 : 0), lineWidth: 2)
                        )
                )
        }
        .buttonStyle(TVButtonStyle())
        .shadow(
            color: isFocused ? Color.blue.opacity(0.4) : Color.clear,
            radius: isFocused ? 15 : 0
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isFocused)
    }
}

// MARK: - Enhanced Button Style

//struct EnhancedButtonStyle: ButtonStyle {
//    enum Style {
//        case primary, secondary
//    }
//    
//    let style: Style
//    
//    init(style: Style = .primary) {
//        self.style = style
//    }
//    
//    func makeBody(configuration: Configuration) -> some View {
//        configuration.label
//            .font(.system(size: 16, weight: .semibold))
//            .foregroundColor(style == .primary ? .black : .white)
//            .padding(.horizontal, 24)
//            .padding(.vertical, 12)
//            .background(
//                RoundedRectangle(cornerRadius: 8)
//                    .fill(style == .primary ? Color.white : Color.white.opacity(0.2))
//            )
//            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
//            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
//    }
//}

// MARK: - Supporting Views (keeping old ones for compatibility)

struct MovieCardView: View {
    let movie: Movie
    let onTap: () -> Void
    @State private var isFocused = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Poster
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 200, height: 300)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .scaleEffect(isFocused ? 1.1 : 1.0)
//                .shadow(color: .black.opacity(0.9), radius: isFocused ? 20 : 10)
                
                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)
                    
                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 200, alignment: .leading)
            }
        }
        .buttonStyle(TVButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

struct CategoryCardView: View {
    let category: Category
    @State private var isFocused = false
    
    var body: some View {
        Button(action: {
            // TODO: Navigate to category view
        }) {
            Text(category.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 150, height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(isFocused ? 0.8 : 0.6))
                )
        }
        .buttonStyle(TVButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }
}

// MARK: - Custom Button Style

struct TVButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.01)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Enhanced Card Views (keeping for compatibility)

struct EnhancedMovieCardView: View {
    let movie: Movie
    let index: Int
    let section: FocusSection
    let onTap: () -> Void

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // Poster
                AsyncImage(url: URL(string: movie.poster ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(2/3, contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .aspectRatio(2/3, contentMode: .fill)
                        .overlay(
                            Image(systemName: "photo")
                                .font(.system(size: 40))
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 200, height: 300)
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .scaleEffect(isFocused ? 1.1 : 1.0)
                .shadow(color: .black.opacity(0.5), radius: isFocused ? 20 : 10)

                // Title and Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(movie.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                        .lineLimit(2)

                    Text("\(movie.displayYear) • \(movie.displayType)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.gray)
                        .lineLimit(1)
                }
                .frame(width: 200, alignment: .leading)
            }
        }
        .buttonStyle(TVButtonStyle())
        .scaleEffect(isFocused ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isFocused)
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            isFocused = focusedItem?.type == .movieCard &&
                       focusedItem?.index == index &&
                       focusedItem?.section == section
        }
    }
}

struct EnhancedCategoryCardView: View {
    let category: Category
    let index: Int

    @Environment(\.focusManager) private var focusManager
    @State private var isFocused = false

    var body: some View {
        Button(action: {
            // TODO: Navigate to category view
        }) {
            Text(category.name)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 150, height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .foregroundColor(Color.blue.opacity(isFocused ? 0.8 : 0.6))
                )
        }
        .buttonStyle(TVButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isFocused)
        .onReceive(focusManager.$currentFocusedItem) { focusedItem in
            isFocused = focusedItem?.type == .categoryCard &&
                       focusedItem?.index == index &&
                       focusedItem?.section == .home
        }
    }
}

#Preview {
    HomeView()
}
